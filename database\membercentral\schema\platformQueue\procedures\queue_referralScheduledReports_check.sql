ALTER PROC dbo.queue_referralScheduledReports_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @grabProcessingStatusID INT, @readyStatusID INT, 
		@processingStatusID INT, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='referralScheduledReports', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@grabProcessingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@processingStatusID OUTPUT;

	-- referralScheduledReports / grabbedForProcessing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -30, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_referralScheduledReports WHERE statusID = @grabProcessingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_referralScheduledReports
		SET statusID = @readyStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @grabProcessingStatusID 
		AND dateUpdated < @timeToUse;

		SET @errorTitle = 'referralScheduledReports Queue Issue';
		SET @errorSubject = 'referralScheduledReports Queue moved items from grabbedForProcessing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- referralScheduledReports / processingItem autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_referralScheduledReports WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_referralScheduledReports
		SET statusID = @readyStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @processingStatusID 
		AND dateUpdated < @timeToUse;

		SET @errorTitle = 'referralScheduledReports Queue Issue';
		SET @errorSubject = 'referralScheduledReports Queue moved items from processingItem to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- referralScheduledReports catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -24, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_referralScheduledReports WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'referralScheduledReports Queue Issue';
		SET @errorSubject = 'referralScheduledReports Queue has items last updated more than 24 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
