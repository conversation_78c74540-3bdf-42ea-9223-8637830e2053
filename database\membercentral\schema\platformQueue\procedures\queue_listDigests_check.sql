ALTER PROC dbo.queue_listDigests_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse DATETIME, @errorSubject VARCHAR(400), @queueTypeID INT, @skippedStatusID INT, 
		@grabProcessingStatusID INT, @readyStatusID INT, @processingStatusID INT, @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='listDigests', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='Skipped', @queueStatusID=@skippedStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@grabProcessingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='Processing', @queueStatusID=@processingStatusID OUTPUT;

	-- listDigests / grabbedForProcessing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(MINUTE, -10, GETDATE());
	SELECT @issueCount = COUNT(itemID) FROM dbo.queue_listDigests WHERE statusID = @grabProcessingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_listDigests
		SET statusID = @readyStatusID, 
			dateUpdated = GETDATE()
		WHERE statusID = @grabProcessingStatusID 
		AND dateUpdated < @timeToUse;
		
		SET @errorTitle = 'listDigests Queue Issue';
		SET @errorSubject = 'listDigests queue moved items from grabbedForProcessing to ReadyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- listDigests / Processing autoreset to ReadyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(MINUTE, -10, GETDATE());
	SELECT @issueCount = COUNT(itemID) FROM dbo.queue_listDigests WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_listDigests
		SET statusID = @readyStatusID, 
			dateUpdated = GETDATE()
		WHERE statusID = @processingStatusID 
		AND dateUpdated < @timeToUse;
		
		SET @errorTitle = 'listDigests Queue Issue';
		SET @errorSubject = 'listDigests queue moved items from processing to ReadyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- notify if more than five attempts
	SELECT @issueCount = COUNT(itemID) FROM dbo.queue_listDigests WHERE previousAttempts >= 5;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'listDigests Queue Issue';
		SET @errorSubject = cast(@issueCount as VARCHAR(10)) + ' ListDigests queue items have errored and attempted at least five times.';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- catch all
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(HOUR, -4, GETDATE())
	SELECT @issueCount = COUNT(itemID) FROM dbo.queue_listDigests WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'listDigests Queue Issue';
		SET @errorSubject = 'listDigests queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- skipped items last updated more than two days
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(DAY, -2, GETDATE())
	SELECT @issueCount = COUNT(itemID) FROM dbo.queue_listDigests WHERE statusID = @skippedStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'listDigests Queue Issue';
		SET @errorSubject = 'listDigests queue has items with skipped status last updated more than 2 days ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END	

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
