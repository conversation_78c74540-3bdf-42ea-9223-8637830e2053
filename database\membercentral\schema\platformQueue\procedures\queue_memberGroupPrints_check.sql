ALTER PROC dbo.queue_MemberGroupPrints_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID INT, @processingStatusID INT, @readyStatusID INT, @itemGroupUID VARCHAR(60), @itemAsStr VARCHAR(60),
		@xmlMessage XML, @issueCount INT, @timeToUse DATETIME, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='MemberGroupPrints', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@processingStatusID OUTPUT;

	-- memberGroupPrints / processingItem autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberGroupPrints WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_memberGroupPrints
		SET statusID = @readyStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @processingStatusID 
		AND dateUpdated < @timeToUse;
		
		SET @errorTitle = 'memberGroupPrints Queue Issue';
		SET @errorSubject = 'memberGroupPrints queue moved items from processingItem to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- memberGroupPrints / readyToProcess resend message
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -30, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberGroupPrints WHERE statusID = @readyStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_memberGroupPrints
		SET dateUpdated = getdate()
			OUTPUT inserted.itemGroupUID 
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE statusID = @readyStatusID 
		AND dateUpdated < @timeToUse;

		SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs;
		while @itemAsStr is not null BEGIN
			SET @xmlMessage = null;
			SELECT @xmlMessage = (SELECT @itemAsStr as [itemGroupUID] for xml path(''), type);
			EXEC dbo.queue_MemberGroupPrints_sendMessage @xmlMessage=@xmlMessage;
			SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		END

		TRUNCATE TABLE #tmpCheckQueueMsgs;

		SET @errorTitle = 'memberGroupPrints Queue Issue';
		SET @errorSubject = 'memberGroupPrints queue resent items in readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END	
	
	-- memberGroupPrints catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberGroupPrints WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'memberGroupPrints Queue Issue';
		SET @errorSubject = 'memberGroupPrints queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
