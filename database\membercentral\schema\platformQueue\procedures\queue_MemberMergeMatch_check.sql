ALTER PROC dbo.queue_MemberMergeMatch_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @readyStatusID INT, @orgID INT, 
		@itemAsStr VARCHAR(60), @xmlMessage xml, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='memberMergeMatch', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;

	-- memberMergeMatch / readyToProcess resend message
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -30, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberMergeMatch WHERE statusID = @readyStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_memberMergeMatch
		SET dateUpdated = getdate()
			OUTPUT inserted.itemGroupUID 
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE statusID = @readyStatusID 
		AND dateUpdated < @timeToUse;

		SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs;
		while @itemAsStr is not null BEGIN
			SELECT @orgID = null, @xmlMessage = null;

			SELECT @orgID = min(orgID)
			FROM dbo.queue_memberMergeMatch
			WHERE itemGroupUID = cast(@itemAsStr as uniqueidentifier);

			SELECT @xmlMessage = isnull((
				SELECT @orgID as o, @itemAsStr as u
				FOR XML RAW('mc'), TYPE
			),'<mc/>');

			EXEC dbo.queue_MemberMergeMatch_sendMessage @xmlMessage=@xmlMessage;
			
			SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		end

		TRUNCATE TABLE #tmpCheckQueueMsgs;

		SET @errorTitle = 'memberMergeMatch Queue Issue';
		SET @errorSubject = 'memberMergeMatch queue resent items in readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- memberMergeMatch catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberMergeMatch WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'memberMergeMatch Queue Issue';
		SET @errorSubject = 'memberMergeMatch queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
