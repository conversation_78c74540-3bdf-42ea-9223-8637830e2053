ALTER PROC dbo.queue_processPreApproveDepoDocs_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse DATETIME, @queueTypeID INT, @processingStatusID INT, @readyStatusID INT, 
		@itemAsStr VARCHAR(60), @xmlMessage xml, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='processPreApproveDepoDocs', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processing', @queueStatusID=@processingStatusID OUTPUT;

	-- processPreApproveDepoDocs / processing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_processPreApproveDepoDocs WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_processPreApproveDepoDocs
		SET statusID = @readyStatusID, 
			dateUpdated = GETDATE()
		WHERE statusID = @processingStatusID 
		AND dateUpdated < @timeToUse;

		SET @errorTitle = 'processPreApproveDepoDocs Queue Issue';
		SET @errorSubject = 'processPreApproveDepoDocs queue moved items from processing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- processPreApproveDepoDocs catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_processPreApproveDepoDocs WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'processPreApproveDepoDocs Queue Issue';
		SET @errorSubject = 'processPreApproveDepoDocs queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
