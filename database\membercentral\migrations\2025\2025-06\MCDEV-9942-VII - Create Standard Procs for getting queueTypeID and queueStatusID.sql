use platformQueue
GO

ALTER PROC dbo.queue_screenshots_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse DATETIME, @queueTypeID INT, @grabProcessingStatusID INT, @readyStatusID INT, 
		@processingStatusID int, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='screenshots', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@grabProcessingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@processingStatusID OUTPUT;

	-- screenshots / grabbedForProcessing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_screenshots WHERE statusID = @grabProcessingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_screenshots
		SET statusID = @readyStatusID, 
			dateUpdated = GETDATE()
		WHERE statusID = @grabProcessingStatusID 
		AND dateUpdated < @timeToUse;

		SET @errorTitle = 'Screenshots Queue Issue';
		SET @errorSubject = 'Screenshots queue moved items from grabbedForProcessing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- screenshots / processingItem autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_screenshots WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_screenshots
		SET statusID = @readyStatusID, 
			dateUpdated = GETDATE()
		WHERE statusID = @processingStatusID 
		AND dateUpdated < @timeToUse;

		SET @errorTitle = 'Screenshots Queue Issue';
		SET @errorSubject = 'Screenshots queue moved items from processingItem to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- screenshots resume task
	SET @issueCount = 0;
	SELECT @issueCount = count(itemID) FROM dbo.queue_screenshots WHERE statusID = @readyStatusID;
	IF @issueCount > 0 
		EXEC membercentral.dbo.sched_resumeTask @name='Process Screenshots Queue', @engine='MCLuceeLinux';

	-- screenshots catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_screenshots WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'Screenshots Queue Issue';
		SET @errorSubject = 'Screenshots queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_schQueries_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount int, @timeToUse datetime, @queueTypeID int, @processingStatusID int, @readyStatusID int, 
		@itemAsStr varchar(60), @xmlMessage xml, @errorSubject varchar(400), @errorTitle varchar(400);
	EXEC dbo.queue_getQueueTypeID @queueType='schQueries', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@processingStatusID OUTPUT;

	-- schQueries / ProcessingItem autoreset to ReadyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -30, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_schQueries WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_schQueries
		SET statusID = @readyStatusID,
			dateUpdated = GETDATE()
			OUTPUT inserted.itemID 
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE statusID = @processingStatusID 
		AND dateUpdated < @timeToUse;

		SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs;
		WHILE @itemAsStr IS NOT NULL BEGIN
			SET @xmlMessage = '<mc i="' + @itemAsStr + '" />';
			EXEC dbo.queue_schQueries_sendMessage @xmlMessage=@xmlMessage;
			SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		END

		TRUNCATE TABLE #tmpCheckQueueMsgs;

		SET @errorTitle = 'schQueries Queue Issue';
		SET @errorSubject = 'schQueries queue resent items in readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- schQueries / ReadyToProcess resend message
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -220, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_schQueries WHERE statusID = @readyStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		update dbo.queue_schQueries
		SET dateUpdated = getdate()
			OUTPUT inserted.itemID 
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE statusID = @readyStatusID 
		AND dateUpdated < @timeToUse;

		SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs;
		WHILE @itemAsStr IS NOT NULL BEGIN
			SET @xmlMessage = '<mc i="' + @itemAsStr + '" />';
			EXEC dbo.queue_schQueries_sendMessage @xmlMessage=@xmlMessage;
			SELECT @itemAsStr = min(itemAsStr) from #tmpCheckQueueMsgs where itemAsStr > @itemAsStr;
		END

		TRUNCATE TABLE #tmpCheckQueueMsgs;

		SET @errorTitle = 'schQueries Queue Issue';
		SET @errorSubject = 'schQueries queue resent items in ReadyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- schQueries catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_schQueries WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'schQueries Queue Issue';
		SET @errorSubject = 'schQueries queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_schLinkedRecordsQuery_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount int, @timeToUse datetime, @queueTypeID int, @processingStatusID int, @readyStatusID int, 
		@itemAsStr varchar(60), @xmlMessage xml, @errorSubject varchar(400), @errorTitle varchar(400);
	EXEC dbo.queue_getQueueTypeID @queueType='schLinkedRecordsQuery', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@processingStatusID OUTPUT;

	-- schLinkedRecordsQuery / ProcessingItem autoreset to ReadyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -30, GETDATE());
	SELECT @issueCount = COUNT(itemID) FROM dbo.queue_schLinkedRecordsQuery WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_schLinkedRecordsQuery
		SET statusID = @readyStatusID,
			dateUpdated = GETDATE()
			OUTPUT inserted.itemID 
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE statusID = @processingStatusID 
		AND dateUpdated < @timeToUse;

		SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs;
		WHILE @itemAsStr IS NOT NULL BEGIN
			SET @xmlMessage = '<mc i="' + @itemAsStr + '" />';
			EXEC dbo.queue_schLinkedRecordsQuery_sendMessage @xmlMessage=@xmlMessage;
			SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		END

		TRUNCATE TABLE #tmpCheckQueueMsgs;

		SET @errorTitle = 'schLinkedRecordsQuery Queue Issue';
		SET @errorSubject = 'schLinkedRecordsQuery queue resent items in readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- schLinkedRecordsQuery / ReadyToProcess resend message
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -220, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_schLinkedRecordsQuery WHERE statusID = @readyStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		update dbo.queue_schLinkedRecordsQuery
		SET dateUpdated = getdate()
			OUTPUT inserted.itemID 
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE statusID = @readyStatusID 
		AND dateUpdated < @timeToUse;

		SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs;
		WHILE @itemAsStr IS NOT NULL BEGIN
			SET @xmlMessage = '<mc i="' + @itemAsStr + '" />';
			EXEC dbo.queue_schLinkedRecordsQuery_sendMessage @xmlMessage=@xmlMessage;
			SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		END

		TRUNCATE TABLE #tmpCheckQueueMsgs;

		SET @errorTitle = 'schLinkedRecordsQuery Queue Issue';
		SET @errorSubject = 'schLinkedRecordsQuery queue resent items in ReadyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- schLinkedRecordsQuery catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = COUNT(itemID) FROM dbo.queue_schLinkedRecordsQuery WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'schLinkedRecordsQuery Queue Issue';
		SET @errorSubject = 'schLinkedRecordsQuery queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_schedReportChangeNotify_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @grabProcessingStatusID INT, @readyStatusID INT, 
		@processingStatusID int, @itemAsStr VARCHAR(60), @xmlMessage xml, @errorSubject VARCHAR(400), @errorTitle varchar(400);
	EXEC dbo.queue_getQueueTypeID @queueType='schedReportChangeNotify', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@grabProcessingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='Processing', @queueStatusID=@processingStatusID OUTPUT;

	-- schedReportChangeNotify / GrabbedForProcessing autoreset to ReadyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_schedReportChangeNotify WHERE statusID = @grabProcessingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_schedReportChangeNotify
		SET statusID = @readyStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @grabProcessingStatusID 
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='Scheduled Report Change Notification', @engine='MCLuceeLinux';

		SET @errorTitle = 'schedReportChangeNotify Queue Issue';
		SET @errorSubject = 'schedReportChangeNotify queue moved items from GrabbedForProcessing to ReadyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- schedReportChangeNotify / Processing autoreset to ReadyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_schedReportChangeNotify WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_schedReportChangeNotify
		SET statusID = @readyStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @processingStatusID 
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='Scheduled Report Change Notification', @engine='MCLuceeLinux';

		SET @errorTitle = 'schedReportChangeNotify Queue Issue';
		SET @errorSubject = 'schedReportChangeNotify queue moved items from Processing to ReadyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- schedReportChangeNotify catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_schedReportChangeNotify WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'schedReportChangeNotify Queue Issue';
		SET @errorSubject = 'schedReportChangeNotify queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_SageCCCIMTest_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @grabProcessingStatusID INT, @readyStatusID INT, 
		@processingStatusID int, @itemAsStr VARCHAR(60), @xmlMessage xml, @errorSubject VARCHAR(400), @errorTitle varchar(400);
	EXEC dbo.queue_getQueueTypeID @queueType='SageCCCIMTest', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@grabProcessingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='Processing', @queueStatusID=@processingStatusID OUTPUT;

	-- SageCCCIMTest / grabbedForProcessing autoreset to ReadyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_SageCCCIMTest WHERE statusID = @grabProcessingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_SageCCCIMTest
		SET statusID = @readyStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @grabProcessingStatusID 
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='API Testing - SageCCCIM', @engine='MCLuceeLinux';

		SET @errorTitle = 'SageCCCIMTest Queue Issue';
		SET @errorSubject = 'SageCCCIMTest queue moved items from grabbedForProcessing to ReadyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- SageCCCIMTest / Processing autoreset to ReadyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -20, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_SageCCCIMTest WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_SageCCCIMTest
		SET statusID = @readyStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @processingStatusID 
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='API Testing - SageCCCIM', @engine='MCLuceeLinux';

		SET @errorTitle = 'SageCCCIMTest Queue Issue';
		SET @errorSubject = 'SageCCCIMTest queue moved items from Processing to ReadyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- SageCCCIMTest catch all
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_SageCCCIMTest WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'SageCCCIMTest Queue Issue';
		SET @errorSubject = 'SageCCCIMTest queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_s3Verify_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @readyStatusID INT, @failedStatusID INT, 
		@errorSubject VARCHAR(400), @errorTitle VARCHAR(400), @tier varchar(12);
	EXEC dbo.queue_getQueueTypeID @queueType='s3Verify', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='failed', @queueStatusID=@failedStatusID OUTPUT;

	-- We dont verify when not in production, so delete the queue when not in prod
	SELECT @tier = tier from membercentral.dbo.fn_getServerSettings();
	IF @tier = 'Production' BEGIN
		-- s3Verify readyToProcess notify
		SET @issueCount = 0;
		SET @timeToUse = DATEADD(hour, -4, GETDATE());
		SELECT @issueCount = count(fileID) FROM dbo.queue_S3Verify WHERE statusID = @readyStatusID AND dateUpdated < @timeToUse;
		IF @issueCount > 0 BEGIN
			SET @errorTitle = 'S3Verify Queue Issue';
			SET @errorSubject = 'S3Verify queue has items in readyToProcess with dateUpdated older than 4 hours.';
			EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
		END

		-- s3Verify failed notify. Use DateAdded since we change dateupdated upon fail but still keep trying to process it.
		SET @issueCount = 0;
		SET @timeToUse = DATEADD(hour, -6, GETDATE());
		SELECT @issueCount = count(fileID) FROM dbo.queue_S3Verify WHERE statusID = @failedStatusID AND dateAdded < @timeToUse;
		IF @issueCount > 0
			DELETE FROM dbo.queue_s3Verify
			WHERE statusID = @failedStatusID 
			AND dateAdded < @timeToUse;
	END ELSE BEGIN
		SELECT @issueCount = count(fileID) FROM dbo.queue_S3Verify;
		IF @issueCount > 0
			DELETE FROM dbo.queue_S3Verify;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_s3Upload_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @readyStatusID INT, @failedStatusID INT, 
		@errorSubject VARCHAR(400), @errorTitle VARCHAR(400), @tier varchar(12);
	EXEC dbo.queue_getQueueTypeID @queueType='s3Upload', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='failed', @queueStatusID=@failedStatusID OUTPUT;

	-- We dont upload when not in production, so delete the queue when not in prod
	SELECT @tier = tier from membercentral.dbo.fn_getServerSettings();
	IF @tier = 'Production' BEGIN
		-- s3Upload readyToProcess notify
		SET @issueCount = 0;
		SET @timeToUse = DATEADD(hour, -4, GETDATE());
		SELECT @issueCount = count(fileID) FROM dbo.queue_S3Upload WHERE statusID = @readyStatusID AND dateUpdated < @timeToUse;
		IF @issueCount > 0 BEGIN
			SET @errorTitle = 'S3Upload Queue Issue';
			SET @errorSubject = 'S3Upload queue has items in readyToProcess with dateUpdated older than 4 hours.';
			EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
		END

		-- s3Upload failed notify. Use DateAdded since we change dateupdated upon fail but still keep trying to process it.
		SET @issueCount = 0;
		SET @timeToUse = DATEADD(hour, -4, GETDATE());
		SELECT @issueCount = count(fileID) FROM dbo.queue_S3Upload WHERE statusID = @failedStatusID AND dateAdded < @timeToUse AND isNotified = 0;
		IF @issueCount > 0 BEGIN
			UPDATE dbo.queue_S3Upload 
			SET isNotified = 1 
			WHERE statusID = @failedStatusID 
			AND dateAdded < @timeToUse 
			AND isNotified = 0;

			SET @errorTitle = 'S3Upload Queue Issue';
			SET @errorSubject = 'S3Upload queue has items in failed status with dateAdded older than 4 hours.';
			EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
		END
	END ELSE BEGIN
		SELECT @issueCount = count(fileID) FROM dbo.queue_S3Upload;
		IF @issueCount > 0
			DELETE FROM dbo.queue_S3Upload;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_s3Rename_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @readyStatusID INT, @failedStatusID INT, 
		@errorSubject VARCHAR(400), @errorTitle VARCHAR(400), @tier varchar(12);
	EXEC dbo.queue_getQueueTypeID @queueType='s3Rename', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='failed', @queueStatusID=@failedStatusID OUTPUT;

	-- We dont rename when not in production, so delete the queue when not in prod
	SELECT @tier = tier from membercentral.dbo.fn_getServerSettings();
	IF @tier = 'Production' BEGIN
		-- s3Rename readyToProcess notify
		SET @issueCount = 0;
		SET @timeToUse = DATEADD(hour, -4, GETDATE());
		SELECT @issueCount = count(fileID) FROM dbo.queue_S3Rename WHERE statusID = @readyStatusID AND dateUpdated < @timeToUse;
		IF @issueCount > 0 BEGIN
			SET @errorTitle = 'S3Rename Queue Issue';
			SET @errorSubject = 'S3Rename queue has items in readyToProcess with dateUpdated older than 4 hours.';
			EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
		END

		-- s3Rename failed notify. Use DateAdded since we change dateupdated upon fail but still keep trying to process it.
		SET @issueCount = 0;
		SET @timeToUse = DATEADD(hour, -4, GETDATE());
		SELECT @issueCount = count(fileID) FROM dbo.queue_S3Rename WHERE statusID = @failedStatusID AND dateAdded < @timeToUse;
		IF @issueCount > 0 BEGIN
			SET @errorTitle = 'S3Rename Queue Issue';
			SET @errorSubject = 'S3Rename queue has items in failed status with dateAdded older than 4 hours.';
			EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
		END
	END ELSE BEGIN
		SELECT @issueCount = count(fileID) FROM dbo.queue_S3Rename;
		IF @issueCount > 0
			DELETE FROM dbo.queue_S3Rename;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_s3Download_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @readyStatusID INT, @failedStatusID INT, 
		@errorSubject VARCHAR(400), @errorTitle VARCHAR(400), @tier varchar(12);
	EXEC dbo.queue_getQueueTypeID @queueType='s3Download', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='failed', @queueStatusID=@failedStatusID OUTPUT;

	-- We dont download when not in production, so delete the queue when not in prod
	SELECT @tier = tier from membercentral.dbo.fn_getServerSettings();
	IF @tier = 'Production' BEGIN
		-- s3Download readyToProcess notify
		SET @issueCount = 0;
		SET @timeToUse = DATEADD(hour, -4, GETDATE());
		SELECT @issueCount = count(fileID) FROM dbo.queue_S3Download WHERE statusID = @readyStatusID AND dateUpdated < @timeToUse;
		IF @issueCount > 0 BEGIN
			SET @errorTitle = 'S3Download Queue Issue';
			SET @errorSubject = 'S3Download queue has items in readyToProcess with dateUpdated older than 4 hours.';
			EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
		END

		-- s3Download failed notify. Use DateAdded since we change dateupdated upon fail but still keep trying to process it.
		SET @issueCount = 0;
		SET @timeToUse = DATEADD(hour, -4, GETDATE());
		SELECT @issueCount = count(fileID) FROM dbo.queue_S3Download WHERE statusID = @failedStatusID AND dateAdded < @timeToUse;
		IF @issueCount > 0 BEGIN
			SET @errorTitle = 'S3Download Queue Issue';
			SET @errorSubject = 'S3Download queue has items in failed status with dateAdded older than 4 hours.';
			EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
		END
	END ELSE BEGIN
		SELECT @issueCount = count(fileID) FROM dbo.queue_S3Download;
		IF @issueCount > 0
			DELETE FROM dbo.queue_S3Download;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_s3Delete_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @readyStatusID INT, @failedStatusID INT, 
		@errorSubject VARCHAR(400), @errorTitle VARCHAR(400), @tier varchar(12);
	EXEC dbo.queue_getQueueTypeID @queueType='s3Delete', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='failed', @queueStatusID=@failedStatusID OUTPUT;

	-- We dont delete when not in production, so delete the queue when not in prod
	SELECT @tier = tier from membercentral.dbo.fn_getServerSettings();
	IF @tier = 'Production' BEGIN
		-- s3Delete readyToProcess notify
		SET @issueCount = 0;
		SET @timeToUse = DATEADD(hour, -4, GETDATE());
		SELECT @issueCount = count(fileID) FROM dbo.queue_S3Delete WHERE statusID = @readyStatusID AND dateUpdated < @timeToUse;
		IF @issueCount > 0 BEGIN
			SET @errorTitle = 'S3Delete Queue Issue';
			SET @errorSubject = 'S3Delete queue has items in readyToProcess with dateUpdated older than 4 hours.';
			EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
		END

		-- s3Delete failed notify. Use DateAdded since we change dateupdated upon fail but still keep trying to process it.
		SET @issueCount = 0;
		SET @timeToUse = DATEADD(hour, -4, GETDATE());
		SELECT @issueCount = count(fileID) FROM dbo.queue_S3Delete WHERE statusID = @failedStatusID AND dateAdded < @timeToUse;
		IF @issueCount > 0 BEGIN
			SET @errorTitle = 'S3Delete Queue Issue';
			SET @errorSubject = 'S3Delete queue has items in failed status with dateAdded older than 4 hours.';
			EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
		END
	END ELSE BEGIN
		SELECT @issueCount = count(fileID) FROM dbo.queue_S3Delete;
		IF @issueCount > 0
			DELETE FROM dbo.queue_S3Delete;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_s3Copy_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @readyStatusID INT, @failedStatusID INT, 
		@errorSubject VARCHAR(400), @errorTitle VARCHAR(400), @tier varchar(12);
	EXEC dbo.queue_getQueueTypeID @queueType='s3Copy', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='failed', @queueStatusID=@failedStatusID OUTPUT;

	-- We dont copy when not in production, so delete the queue when not in prod
	SELECT @tier = tier from membercentral.dbo.fn_getServerSettings();
	IF @tier = 'Production' BEGIN
		-- s3Copy readyToProcess notify
		SET @issueCount = 0;
		SET @timeToUse = DATEADD(hour, -4, GETDATE());
		SELECT @issueCount = count(fileID) FROM dbo.queue_s3Copy WHERE statusID = @readyStatusID AND dateUpdated < @timeToUse;
		IF @issueCount > 0 BEGIN
			SET @errorTitle = 'S3Copy Queue Issue';
			SET @errorSubject = 'S3Copy queue has items in readyToProcess with dateUpdated older than 4 hours.';
			EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
		END

		-- s3Copy failed notify. Use DateAdded since we change dateupdated upon fail but still keep trying to process it.
		SET @issueCount = 0;
		SET @timeToUse = DATEADD(hour, -4, GETDATE());
		SELECT @issueCount = count(fileID) FROM dbo.queue_s3Copy WHERE statusID = @failedStatusID AND dateAdded < @timeToUse;
		IF @issueCount > 0 BEGIN
			SET @errorTitle = 'S3Copy Queue Issue';
			SET @errorSubject = 'S3Copy queue has items in failed status with dateAdded older than 4 hours.';
			EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
		END
	END ELSE BEGIN
		SELECT @issueCount = count(fileID) FROM dbo.queue_s3Copy;
		IF @issueCount > 0
			DELETE FROM dbo.queue_s3Copy;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_refreshMemberPhoto_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @processingStatusID INT, @readyStatusID INT, 
		@itemAsStr VARCHAR(60), @xmlMessage xml, @errorSubject VARCHAR(400), @errorTitle varchar(400);
	EXEC dbo.queue_getQueueTypeID @queueType='refreshMemberPhoto', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='Processing', @queueStatusID=@processingStatusID OUTPUT;

	-- refreshMemberPhoto / Processing autoreset to ReadyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -20, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_refreshMemberPhoto WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_refreshMemberPhoto
		SET statusID = @readyStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @processingStatusID 
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='Process Refresh MemberPhoto Queue', @engine='BERLinux';

		SET @errorTitle = 'refreshMemberPhoto Queue Issue';
		SET @errorSubject = 'refreshMemberPhoto queue moved items from Processing to ReadyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- refreshMemberPhoto catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_refreshMemberPhoto WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'refreshMemberPhoto Queue Issue';
		SET @errorSubject = 'refreshMemberPhoto queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_refreshDashboardObjects_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse DATETIME, @queueTypeID INT, @grabProcessingStatusID INT, @readyStatusID INT, 
		@processingStatusID INT, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='refreshDashboardObjects', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@grabProcessingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processing', @queueStatusID=@processingStatusID OUTPUT;

	-- refreshDashboardObjects / grabbedForProcessing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_refreshDashboardObjects WHERE statusID = @grabProcessingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_refreshDashboardObjects
		SET statusID = @readyStatusID, 
			dateUpdated = GETDATE()
		WHERE statusID = @grabProcessingStatusID 
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='Process Refresh Dashboard Objects Queue', @engine='MCLuceeLinux';

		SET @errorTitle = 'refreshDashboardObjects Queue Issue';
		SET @errorSubject = 'refreshDashboardObjects queue moved items from grabbedForProcessing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- refreshDashboardObjects / processing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_refreshDashboardObjects WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_refreshDashboardObjects
		SET statusID = @readyStatusID, 
			dateUpdated = GETDATE()
		WHERE statusID = @processingStatusID 
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='Process Refresh Dashboard Objects Queue', @engine='MCLuceeLinux';

		SET @errorTitle = 'refreshDashboardObjects Queue Issue';
		SET @errorSubject = 'refreshDashboardObjects queue moved items from processing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- refreshDashboardObjects catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_refreshDashboardObjects WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'refreshDashboardObjects Queue Issue';
		SET @errorSubject = 'refreshDashboardObjects queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_referralScheduledReports_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @grabProcessingStatusID INT, @readyStatusID INT, 
		@processingStatusID INT, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='referralScheduledReports', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@grabProcessingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@processingStatusID OUTPUT;

	-- referralScheduledReports / grabbedForProcessing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -30, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_referralScheduledReports WHERE statusID = @grabProcessingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_referralScheduledReports
		SET statusID = @readyStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @grabProcessingStatusID 
		AND dateUpdated < @timeToUse;

		SET @errorTitle = 'referralScheduledReports Queue Issue';
		SET @errorSubject = 'referralScheduledReports Queue moved items from grabbedForProcessing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- referralScheduledReports / processingItem autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_referralScheduledReports WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_referralScheduledReports
		SET statusID = @readyStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @processingStatusID 
		AND dateUpdated < @timeToUse;

		SET @errorTitle = 'referralScheduledReports Queue Issue';
		SET @errorSubject = 'referralScheduledReports Queue moved items from processingItem to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- referralScheduledReports catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -24, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_referralScheduledReports WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'referralScheduledReports Queue Issue';
		SET @errorSubject = 'referralScheduledReports Queue has items last updated more than 24 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_pubSyndIssueDist_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @grabProcessingStatusID INT, @readyStatusID INT,
		@processingStatusID INT, @itemAsStr VARCHAR(60), @xmlMessage xml, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='pubSyndIssueDist', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@grabProcessingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processing', @queueStatusID=@processingStatusID OUTPUT;

	-- queue_pubSyndIssueDist / grabbedForProcessing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_pubSyndIssueDist WHERE statusID = @grabProcessingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_pubSyndIssueDist
		SET statusID = @readyStatusID,
			dateUpdated = getdate()
		WHERE statusID = @grabProcessingStatusID
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='Process Syndicated Issue Distribution Queue', @engine='MCLuceeLinux';

		SET @errorTitle = 'pubSyndIssueDist Queue Issue';
		SET @errorSubject = 'pubSyndIssueDist queue moved items from grabbedForProcessing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- queue_pubSyndIssueDist / processing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_pubSyndIssueDist WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_pubSyndIssueDist
		SET statusID = @readyStatusID,
			dateUpdated = getdate()
		WHERE statusID = @processingStatusID
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='Process Syndicated Issue Distribution Queue', @engine='MCLuceeLinux';

		SET @errorTitle = 'pubSyndIssueDist Queue Issue';
		SET @errorSubject = 'pubSyndIssueDist queue moved items from processing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- queue_pubSyndIssueDist catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_pubSyndIssueDist WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'pubSyndIssueDist Queue Issue';
		SET @errorSubject = 'pubSyndIssueDist queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_processPreApproveDepoDocs_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse DATETIME, @queueTypeID INT, @processingStatusID INT, @readyStatusID INT, 
		@itemAsStr VARCHAR(60), @xmlMessage xml, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='processPreApproveDepoDocs', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processing', @queueStatusID=@processingStatusID OUTPUT;

	-- processPreApproveDepoDocs / processing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_processPreApproveDepoDocs WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_processPreApproveDepoDocs
		SET statusID = @readyStatusID, 
			dateUpdated = GETDATE()
		WHERE statusID = @processingStatusID 
		AND dateUpdated < @timeToUse;

		SET @errorTitle = 'processPreApproveDepoDocs Queue Issue';
		SET @errorSubject = 'processPreApproveDepoDocs queue moved items from processing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- processPreApproveDepoDocs catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_processPreApproveDepoDocs WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'processPreApproveDepoDocs Queue Issue';
		SET @errorSubject = 'processPreApproveDepoDocs queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_payTSBalance_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @grabProcessingStatusID INT, @readyStatusID INT, 
		@processingStatusID INT, @itemAsStr VARCHAR(60), @xmlMessage xml, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='payTSBalance', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@grabProcessingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingPayment', @queueStatusID=@processingStatusID OUTPUT;

	-- payTSBalance / grabbedForProcessing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -30, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_payTSBalance WHERE statusID = @grabProcessingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_payTSBalance
		SET statusID = @readyStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @grabProcessingStatusID 
		AND dateUpdated < @timeToUse;

		SET @errorTitle = 'PayTSBalance Queue Issue';
		SET @errorSubject = 'PayTSBalance Queue moved items from grabbedForProcessing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- payTSBalance / processingPayment with dateupdated older than 10 mins, something went wrong
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_payTSBalance WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'PayTSBalance Queue Issue';
		SET @errorSubject = 'PayTSBalance queue has items in processingPayment with dateupdated older than 10 minutes.';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- payTSBalance catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = COUNT(itemID) FROM dbo.queue_payTSBalance WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'PayTSBalance Queue Issue';
		SET @errorSubject = 'PayTSBalance Queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_payInvoices_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @grabProcessingStatusID INT, @readyStatusID INT, 
		@processingPayStatusID INT, @processingAllocStatusID INT, @readyNotifyStatusID INT, @grabNotifyStatusID INT, 
		@errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='payInvoices', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@grabProcessingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingPayment', @queueStatusID=@processingPayStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingAllocation', @queueStatusID=@processingAllocStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@readyNotifyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@grabNotifyStatusID OUTPUT;

	-- PayInvoices / grabbedForProcessing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -30, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_payInvoices WHERE statusID = @grabProcessingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_payInvoices
		SET statusID = @readyStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @grabProcessingStatusID 
		AND dateUpdated < @timeToUse;

		SET @errorTitle = 'PayInvoices Queue Issue';
		SET @errorSubject = 'PayInvoices Queue moved items from grabbedForProcessing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- PayInvoices / processingPayment or processingAllocation with dateupdated older than 10 mins, something else in the group must be stopping it
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_payInvoices WHERE statusID in (@processingPayStatusID,@processingAllocStatusID) AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'PayInvoices Queue Issue';
		SET @errorSubject = 'PayInvoices queue has items in processingPayment or processingAllocation with dateupdated older than 10 minutes.';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- PayInvoices / readyToNotify or grabbedForNotifying with dateupdated older than 2 hours, something else in the group must be stopping it
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -2, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_payInvoices WHERE statusID in (@readyNotifyStatusID,@grabNotifyStatusID) AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'PayInvoices Queue Issue';
		SET @errorSubject = 'PayInvoices queue has items in readyToNotify or grabbedForNotifying with last updated more than 2 hours ago.';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- PayInvoices catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = COUNT(itemID) FROM dbo.queue_payInvoices WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'PayInvoices Queue Issue';
		SET @errorSubject = 'PayInvoices Queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_paperStatements_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @grabProcessingStatusID INT, @readyStatusID INT, 
		@grabNotifyStatusID INT, @readyNotifyStatusID INT, @processingStatusID INT, @itemAsStr VARCHAR(60), @xmlMessage xml, 
		@errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='paperStatements', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@grabProcessingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@processingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@readyNotifyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@grabNotifyStatusID OUTPUT;

	-- paperStatements / grabbedForProcessing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemUID) FROM dbo.tblQueueItems WHERE queueStatusID = @grabProcessingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.tblQueueItems
		SET queueStatusID = @readyStatusID, 
			dateUpdated = getdate(),
			jobUID = null,
			jobDateStarted = null
		WHERE queueStatusID = @grabProcessingStatusID 
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='Paper Statements Queue', @engine='BERLinux';

		SET @errorTitle = 'paperStatements Queue Issue';
		SET @errorSubject = 'paperStatements queue moved items from grabbedForProcessing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- paperStatements / grabbedForNotifying autoreset to readyToNotify
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemUID) FROM dbo.tblQueueItems WHERE queueStatusID = @grabNotifyStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.tblQueueItems
		SET queueStatusID = @readyNotifyStatusID, 
			dateUpdated = getdate(),
			jobUID = null,
			jobDateStarted = null
		WHERE queueStatusID = @grabNotifyStatusID 
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='Paper Statements Queue', @engine='BERLinux';

		SET @errorTitle = 'paperStatements Queue Issue';
		SET @errorSubject = 'paperStatements queue moved items from grabbedForNotifying to readyToNotify';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- paperStatements / processingItem with dateupdated older than 10 minutes
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemUID) FROM dbo.tblQueueItems WHERE queueStatusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.tblQueueItems
		SET queueStatusID = @readyStatusID, 
			dateUpdated = getdate(),
			jobUID = null,
			jobDateStarted = null
		WHERE queueStatusID = @processingStatusID 
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='Paper Statements Queue', @engine='BERLinux';

		SET @errorTitle = 'paperStatements Queue Issue';
		SET @errorSubject = 'paperStatements queue has items in processingItem with dateupdated older than 10 minutes.';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- paperStatements catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(qi.itemUID)
		FROM dbo.tblQueueItems AS qi 
		INNER JOIN dbo.tblQueueStatuses AS qis ON qis.queueStatusID = qi.queueStatusID
		INNER JOIN dbo.tblQueueTypes AS qt ON qt.queueTypeID = qis.queueTypeID
		WHERE qt.queueTypeID = @queueTypeID
		AND (
			(qi.dateUpdated < @timeToUse AND qi.jobUID is null)
			OR
			qi.JobDateStarted < @timeToUse
		);
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'paperStatements Queue Issue';
		SET @errorSubject = 'paperStatements queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_NJFirmSubStatements_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @grabProcessingStatusID INT, @readyStatusID INT, 
		@processingStatusID INT, @readyNotifyStatusID INT, @grabNotifyStatusID INT, 
		@errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='NJFirmSubStatements', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@grabProcessingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingFirm', @queueStatusID=@processingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@readyNotifyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@grabNotifyStatusID OUTPUT;

	-- NJFirmSubStatements / grabbedForProcessing autoreset to readyToProcess
	set @issueCount = 0;
	set @timeToUse = DATEADD(minute, -10, GETDATE());
	select @issueCount = count(itemID) from dbo.queue_NJFirmSubStatements where statusID = @grabProcessingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		update dbo.queue_NJFirmSubStatements
		set statusID = @readyStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @grabProcessingStatusID 
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='NJ Firm Billing Report Queue', @engine='BERLinux';

		SET @errorTitle = 'NJFirmSubStatements Queue Issue';
		SET @errorSubject = 'NJFirmSubStatements Queue moved items from grabbedForProcessing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- NJFirmSubStatements / processingFirm autoreset to readyToProcess
	set @issueCount = 0;
	set @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_NJFirmSubStatements WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		update dbo.queue_NJFirmSubStatements
		set statusID = @readyStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @processingStatusID 
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='NJ Firm Billing Report Queue', @engine='BERLinux';

		SET @errorTitle = 'NJFirmSubStatements Queue Issue';
		SET @errorSubject = 'NJFirmSubStatements queue moved items from processingFirm to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- NJFirmSubStatements / readyToNotify or grabbedForNotifying with dateupdated older than 4 hours, something else in the group must be stopping it
	set @issueCount = 0;
	set @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_NJFirmSubStatements WHERE statusID in (@readyNotifyStatusID,@grabNotifyStatusID) AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'NJFirmSubStatements Queue Issue';
		SET @errorSubject = 'NJFirmSubStatements queue has items in readyToNotify or grabbedForNotifying last updated more than 4 hours ago.';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- NJFirmSubStatements catchall
	set @issueCount = 0;
	set @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = COUNT(itemID) FROM dbo.queue_NJFirmSubStatements WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'NJFirmSubStatements Queue Issue';
		SET @errorSubject = 'NJFirmSubStatements Queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_moveGroupUsage_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @grabProcessingStatusID INT, @readyStatusID INT, 
		@processingStatusID INT, @itemAsStr VARCHAR(60), @xmlMessage xml, @errorSubject VARCHAR(400), @errorTitle varchar(400);
	EXEC dbo.queue_getQueueTypeID @queueType='moveGroupUsage', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@grabProcessingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='Processing', @queueStatusID=@processingStatusID OUTPUT;

	-- moveGroupUsage / GrabbedForProcessing autoreset to ReadyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_moveGroupUsage WHERE statusID = @grabProcessingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_moveGroupUsage
		SET statusID = @readyStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @grabProcessingStatusID 
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='Process Move Group Usage Queue', @engine='BERLinux';

		SET @errorTitle = 'moveGroupUsage Queue Issue';
		SET @errorSubject = 'moveGroupUsage queue moved items from GrabbedForProcessing to ReadyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- moveGroupUsage / Processing autoreset to ReadyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_moveGroupUsage WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_moveGroupUsage
		SET statusID = @readyStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @processingStatusID 
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='Process Move Group Usage Queue', @engine='BERLinux';

		SET @errorTitle = 'moveGroupUsage Queue Issue';
		SET @errorSubject = 'moveGroupUsage queue moved items from Processing to ReadyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- moveGroupUsage catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_moveGroupUsage WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'moveGroupUsage Queue Issue';
		SET @errorSubject = 'moveGroupUsage queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_MemberUpdate_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID INT, @readyStatusID INT, @processingStatusID INT, @doneStatusID INT, @jobID INT,
		@errorSubject VARCHAR(400), @jobCount INT, @issueCount INT, @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='MemberImport', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='Processing', @queueStatusID=@processingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='done', @queueStatusID=@doneStatusID OUTPUT;

	IF OBJECT_ID('tempdb..#tmpMemImportJobs') IS NOT NULL 
		DROP TABLE #tmpMemImportJobs;
	CREATE TABLE #tmpMemImportJobs (jobID INT, readyToProcessMemCount INT, jobSinceMinutes INT);

	-- MemberImport / Done
	IF EXISTS (
		SELECT distinct imp.jobID
		FROM dbo.memimport_members as imp
		INNER JOIN dbo.memimport_jobs as j on j.jobID = imp.jobID
		WHERE imp.queueStatusID = @doneStatusID
		AND datediff(minute,j.dateEntered,getdate()) >= 30
			except
		SELECT distinct jobID
		FROM dbo.memimport_members
		WHERE queueStatusID <> @doneStatusID
	) BEGIN
		-- populate the log table for logging. we dont know the logID so just put 0.
		IF OBJECT_ID('tempdb..#tmpPMILogID') IS NOT NULL 
			DROP TABLE #tmpPMILogID;
		CREATE TABLE #tmpPMILogID (logID INT PRIMARY KEY);

		DECLARE @tmpJobs TABLE (jobID INT);
		
		INSERT INTO @tmpJobs (jobID)
		SELECT distinct imp.jobID
		FROM dbo.memimport_members as imp
		INNER JOIN dbo.memimport_jobs as j on j.jobID = imp.jobID
		WHERE imp.queueStatusID = @doneStatusID
		AND datediff(minute,j.dateEntered,getdate()) >= 30
			except
		SELECT distinct jobID
		FROM dbo.memimport_members
		WHERE queueStatusID <> @doneStatusID;

		SELECT @jobID = MIN(jobID) FROM @tmpJobs;
		WHILE @jobID IS NOT NULL BEGIN
			TRUNCATE TABLE #tmpPMILogID;

			INSERT INTO #tmpPMILogID (logID) VALUES (0);
			
			EXEC memberCentral.dbo.ams_importPartialMemberData_cancel @jobID=@jobID;

			SELECT @jobID = MIN(jobID) FROM @tmpJobs WHERE jobID > @jobID;
		END

		SET @errorTitle = 'MemberImport Queue Issue';
		SET @errorSubject = 'MemberImport job was stuck in done status for 30 minutes AND removed';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;

		IF OBJECT_ID('tempdb..#tmpPMILogID') IS NOT NULL 
			DROP TABLE #tmpPMILogID;
	END

	-- members stuck in readyToProcess for more than 30 minutes
	INSERT INTO #tmpMemImportJobs (jobID, readyToProcessMemCount, jobSinceMinutes)
	SELECT j.jobID, count(imp.memberID), datediff(minute,j.dateEntered,getDate())
	FROM dbo.memimport_jobs as j
	INNER JOIN dbo.memimport_members as imp on imp.jobID = j.jobID
	WHERE j.statusID = @readyStatusID
	AND imp.queueStatusID = @readyStatusID
	AND datediff(minute,j.dateEntered,getDate()) >= 30
	group by j.jobID, j.dateEntered;

	SET @jobCount = @@ROWCOUNT;

	IF @jobCount = 0
		GOTO on_done;

	SELECT @issueCount = COUNT(*)
	FROM #tmpMemImportJobs AS tmp
	WHERE tmp.readyToProcessMemCount = 1
	AND NOT EXISTS (SELECT 1 FROM dbo.memimport_members as imp WHERE imp.jobID = tmp.jobID AND imp.queueStatusID <> @readyStatusID);

	-- if a member is stuck in readyToProcess for more than 30 minutes and there are no other members in readyToProcess for any jobs
	IF @jobCount = 1 AND @issueCount = 1 BEGIN
		SELECT @jobID = jobID FROM #tmpMemImportJobs;
		EXEC dbo.queue_MemberUpdate_load @jobID=@jobID;

		SET @errorTitle = 'MemberImport Queue Issue';
		SET @errorSubject = 'MemberImport job member stuck in readyToProcess for more than 30 minutes AND retried';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END
	
	-- multiple members stuck in readyToProcess for more than 4 hours
	ELSE IF EXISTS (SELECT 1 FROM #tmpMemImportJobs WHERE readyToProcessMemCount > 0 AND jobSinceMinutes >= 240) BEGIN
		SET @jobID = NULL;
		SELECT @jobID = MIN(jobID) FROM #tmpMemImportJobs WHERE readyToProcessMemCount > 0 AND jobSinceMinutes >= 240;

		WHILE @jobID IS NOT NULL BEGIN
			EXEC dbo.queue_MemberUpdate_load @jobID=@jobID;
			SELECT @jobID = MIN(jobID) FROM #tmpMemImportJobs WHERE jobID > @jobID AND readyToProcessMemCount > 0 AND jobSinceMinutes >= 240;
		END

		SET @errorTitle = 'MemberImport Queue Issue';
		SET @errorSubject = 'MemberImport job members stuck in readyToProcess for more than 4 hours AND retried';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END
	
	on_done:
	IF OBJECT_ID('tempdb..#tmpMemImportJobs') IS NOT NULL 
		DROP TABLE #tmpMemImportJobs;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_memberSiteDefByOrg_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @readyStatusID INT, @processingStatusID INT, 
		@errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='memberSiteDefByOrg', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@processingStatusID OUTPUT;

	-- memberSiteDefByOrg / processingItem autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberSiteDefByOrg WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_memberSiteDefByOrg
		SET statusID = @readyStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @processingStatusID 
		AND dateUpdated < @timeToUse;
		
		SET @errorTitle = 'memberSiteDefByOrg Queue Issue';
		SET @errorSubject = 'memberSiteDefByOrg queue moved items from processingItem to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- memberSiteDefByOrg catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberSiteDefByOrg WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'memberSiteDefByOrg Queue Issue';
		SET @errorSubject = 'memberSiteDefByOrg queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_memberPhotoThumb_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @processingStatusID INT, @readyStatusID INT, 
		@grabProcessingStatusID INT, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='memberPhotoThumb', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@grabProcessingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingPhoto', @queueStatusID=@processingStatusID OUTPUT;

	-- memberPhotoThumb / processingPhoto autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberPhotoThumb WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_memberPhotoThumb
		SET statusID = @readyStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @processingStatusID 
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='Member Photo Thumbnails Queue', @engine='BERLinux';

		SET @errorTitle = 'MemberPhotoThumb Queue Issue';
		SET @errorSubject = 'MemberPhotoThumb queue moved items from processingPhoto to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- memberPhotoThumb / grabbedForProcessing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberPhotoThumb WHERE statusID = @grabProcessingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_memberPhotoThumb
		SET statusID = @readyStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @grabProcessingStatusID 
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='Member Photo Thumbnails Queue', @engine='BERLinux';

		SET @errorTitle = 'MemberPhotoThumb Queue Issue';
		SET @errorSubject = 'MemberPhotoThumb queue moved items from grabbedForProcessing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- memberPhotoThumb catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberPhotoThumb WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'MemberPhotoThumb Queue Issue';
		SET @errorSubject = 'MemberPhotoThumb queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_MemberMergeMatch_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @readyStatusID INT, @orgID INT, 
		@itemAsStr VARCHAR(60), @xmlMessage xml, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='memberMergeMatch', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;

	-- memberMergeMatch / readyToProcess resend message
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -30, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberMergeMatch WHERE statusID = @readyStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_memberMergeMatch
		SET dateUpdated = getdate()
			OUTPUT inserted.itemGroupUID 
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE statusID = @readyStatusID 
		AND dateUpdated < @timeToUse;

		SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs;
		while @itemAsStr is not null BEGIN
			SELECT @orgID = null, @xmlMessage = null;

			SELECT @orgID = min(orgID)
			FROM dbo.queue_memberMergeMatch
			WHERE itemGroupUID = cast(@itemAsStr as uniqueidentifier);

			SELECT @xmlMessage = isnull((
				SELECT @orgID as o, @itemAsStr as u
				FOR XML RAW('mc'), TYPE
			),'<mc/>');

			EXEC dbo.queue_MemberMergeMatch_sendMessage @xmlMessage=@xmlMessage;
			
			SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		end

		TRUNCATE TABLE #tmpCheckQueueMsgs;

		SET @errorTitle = 'memberMergeMatch Queue Issue';
		SET @errorSubject = 'memberMergeMatch queue resent items in readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- memberMergeMatch catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberMergeMatch WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'memberMergeMatch Queue Issue';
		SET @errorSubject = 'memberMergeMatch queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_memberJoinDates_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse DATETIME, @queueTypeID INT, @processingStatusID INT, @readyStatusID INT, 
		@itemAsStr VARCHAR(60), @xmlMessage xml, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='memberJoinDates', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processing', @queueStatusID=@processingStatusID OUTPUT;

	-- memberJoinDates / processing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberJoinDates WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_memberJoinDates
		SET statusID = @readyStatusID, 
			dateUpdated = GETDATE()
			OUTPUT inserted.itemID 
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE statusID = @processingStatusID 
		AND dateUpdated < @timeToUse;

		SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs;
		WHILE @itemAsStr IS NOT NULL BEGIN
			SET @xmlMessage = '<mc i="' + @itemAsStr + '" />';
			EXEC dbo.queue_memberJoinDates_sendMessage @xmlMessage=@xmlMessage;
			SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		END

		TRUNCATE TABLE #tmpCheckQueueMsgs;

		SET @errorTitle = 'memberJoinDates Queue Issue';
		SET @errorSubject = 'memberJoinDates queue moved items from processing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- memberJoinDates / ReadyToProcess resend message
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -220, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberJoinDates WHERE statusID = @readyStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		update dbo.queue_memberJoinDates
		SET dateUpdated = getdate()
			OUTPUT inserted.itemID 
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE statusID = @readyStatusID 
		AND dateUpdated < @timeToUse;

		SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs;
		WHILE @itemAsStr IS NOT NULL BEGIN
			SET @xmlMessage = '<mc i="' + @itemAsStr + '" />';
			EXEC dbo.queue_memberJoinDates_sendMessage @xmlMessage=@xmlMessage;
			SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		END

		TRUNCATE TABLE #tmpCheckQueueMsgs;

		SET @errorTitle = 'memberJoinDates Queue Issue';
		SET @errorSubject = 'memberJoinDates queue resent items in ReadyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- memberJoinDates catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberJoinDates WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'memberJoinDates Queue Issue';
		SET @errorSubject = 'memberJoinDates queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_MemberGroups_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- dont run this if a full platform population is running; that process takes over an hour AND running this will muck it up
	IF EXISTS (SELECT top 1 runID FROM dataTransfer.dbo.cache_repopulation)
		GOTO on_done;

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @notReadyStatusID INT, @readyStatusID INT, 
		@orgID INT, @itemAsStr VARCHAR(60), @xmlMessage xml, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='memberGroups', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='notReady', @queueStatusID=@notReadyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;

	-- memberGroups / notReady resend message
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -15, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberGroups (READPAST) WHERE statusID = @notReadyStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblNotReadyGroups') IS NOT NULL 
			DROP TABLE #tblNotReadyGroups;
		IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
			DROP TABLE #tblMCQRun;
		CREATE TABLE #tblNotReadyGroups (orgID INT, memberid INT);
		CREATE TABLE #tblMCQRun (orgID INT, memberID INT INDEX IX_tblMCQRun_memberID, conditionID INT);

		INSERT INTO #tblNotReadyGroups (orgID, memberID)
		SELECT orgID, memberID
		FROM dbo.queue_memberGroups (READPAST)
		WHERE statusID = @notReadyStatusID 
		AND dateUpdated < @timeToUse
		group by orgID, memberID;

		DELETE qi WITH (READPAST)
		FROM dbo.queue_memberGroups as qi
		INNER JOIN #tblNotReadyGroups as tmp 
			on tmp.orgID = qi.orgID 
			and qi.statusID = @notReadyStatusID
			AND tmp.memberID = qi.memberID;
		
		SELECT @orgID = min(orgID) FROM #tblNotReadyGroups;
		WHILE @orgID is not null BEGIN
			INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
			SELECT orgID, memberID, null
			FROM #tblNotReadyGroups
			WHERE orgID = @orgID;

			EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroups';

			TRUNCATE TABLE #tblMCQRun;

			SELECT @orgID = min(orgID) FROM #tblNotReadyGroups WHERE orgID > @orgID;
		END

		IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
			DROP TABLE #tblMCQRun;
		IF OBJECT_ID('tempdb..#tblNotReadyGroups') IS NOT NULL 
			DROP TABLE #tblNotReadyGroups;

		SET @errorTitle = 'memberGroups Queue Issue';
		SET @errorSubject = 'memberGroups queue resent items in notReady';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- memberGroups / readyToProcess resend message
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -15, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberGroups (READPAST) WHERE statusID = @readyStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_memberGroups WITH (UPDLOCK, READPAST)
		SET dateUpdated = getdate()
			OUTPUT inserted.itemGroupUID, inserted.orgID
			INTO #tmpCheckQueueMsgs (itemAsStr, orgID)
		WHERE statusID = @readyStatusID 
		AND dateUpdated < @timeToUse;

		SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs;
		while @itemAsStr is not null BEGIN
			SELECT @orgID = orgID from #tmpCheckQueueMsgs where itemAsStr = @itemAsStr;
			
			SELECT @xmlMessage = isnull((	
				SELECT @orgID as o, @itemAsStr as u	
				FOR XML RAW('mc'), TYPE	
			),'<mc/>');

			EXEC dbo.queue_memberGroups_sendMessage @xmlMessage=@xmlMessage;

			SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		end

		TRUNCATE TABLE #tmpCheckQueueMsgs;
		
		SET @errorTitle = 'memberGroups Queue Issue';
		SET @errorSubject = 'memberGroups queue resent items in readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- memberGroups catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -1, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberGroups (READPAST) WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'memberGroups Queue Issue';
		SET @errorSubject = 'memberGroups queue has items last updated more than 1 hour ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_MemberGroupPrints_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID INT, @processingStatusID INT, @readyStatusID INT, @itemGroupUID VARCHAR(60), @itemAsStr VARCHAR(60),
		@xmlMessage XML, @issueCount INT, @timeToUse DATETIME, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='MemberGroupPrints', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@processingStatusID OUTPUT;

	-- memberGroupPrints / processingItem autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberGroupPrints WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_memberGroupPrints
		SET statusID = @readyStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @processingStatusID 
		AND dateUpdated < @timeToUse;
		
		SET @errorTitle = 'memberGroupPrints Queue Issue';
		SET @errorSubject = 'memberGroupPrints queue moved items from processingItem to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- memberGroupPrints / readyToProcess resend message
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -30, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberGroupPrints WHERE statusID = @readyStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_memberGroupPrints
		SET dateUpdated = getdate()
			OUTPUT inserted.itemGroupUID 
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE statusID = @readyStatusID 
		AND dateUpdated < @timeToUse;

		SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs;
		while @itemAsStr is not null BEGIN
			SET @xmlMessage = null;
			SELECT @xmlMessage = (SELECT @itemAsStr as [itemGroupUID] for xml path(''), type);
			EXEC dbo.queue_MemberGroupPrints_sendMessage @xmlMessage=@xmlMessage;
			SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		END

		TRUNCATE TABLE #tmpCheckQueueMsgs;

		SET @errorTitle = 'memberGroupPrints Queue Issue';
		SET @errorSubject = 'memberGroupPrints queue resent items in readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END	
	
	-- memberGroupPrints catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberGroupPrints WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'memberGroupPrints Queue Issue';
		SET @errorSubject = 'memberGroupPrints queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_MemberConditions_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @readyStatusID INT, @orgID INT, @processType VARCHAR(30),
		@itemAsStr VARCHAR(60), @xmlMessage xml, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='memberConditions', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;

	-- memberConditions / readyToProcess resend message
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -60, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberConditions WHERE statusID = @readyStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_memberConditions
		SET dateUpdated = getdate()
			OUTPUT inserted.itemGroupUID 
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE statusID = @readyStatusID 
		AND dateUpdated < @timeToUse;

		SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs;
		while @itemAsStr is not null BEGIN
			SELECT @orgID = null, @processType = null, @xmlMessage = null;

			SELECT @orgID = orgID, @processType = processType 	
			from dbo.queue_memberConditions
			WHERE itemGroupUID = @itemAsStr;

			SELECT @xmlMessage = isnull((	
				SELECT @processType as t, @orgID as o, @itemAsStr as u	
				FOR XML RAW('mc'), TYPE	
			),'<mc/>');

			EXEC dbo.queue_MemberConditions_sendMessage @xmlMessage=@xmlMessage;

			SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		end

		TRUNCATE TABLE #tmpCheckQueueMsgs;
		
		SET @errorTitle = 'memberConditions Queue Issue';
		SET @errorSubject = 'memberConditions queue resent items in readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- memberConditions catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberConditions WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'memberConditions Queue Issue';
		SET @errorSubject = 'memberConditions queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_MCFileDelete_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);

	-- MCFileDelete catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(fileID) FROM dbo.queue_MCFileDelete WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'MCFileDelete Queue Issue';
		SET @errorSubject = 'MCFileDelete queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_lyrisListSync_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount int, @timeToUse datetime, @queueTypeID int, @processingStatusID int, @readyStatusID int, 
		@itemAsStr varchar(60), @xmlMessage xml, @errorSubject varchar(400), @errorTitle varchar(400);
	EXEC dbo.queue_getQueueTypeID @queueType='lyrisListSync', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@processingStatusID OUTPUT;

	-- lyrisListSync / processingItem autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -1, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_lyrisListSync WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_lyrisListSync
		SET statusID = @readyStatusID, 
			dateUpdated = GETDATE()
			OUTPUT inserted.itemID 
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE statusID = @processingStatusID 
		AND dateUpdated < @timeToUse;

		-- resend messages
		SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs;
		WHILE @itemAsStr IS NOT NULL BEGIN
			SET @xmlMessage = '<mc i="' + @itemAsStr + '" />';
			EXEC dbo.queue_lyrisListSync_sendMessage @xmlMessage=@xmlMessage;
			SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		END

		TRUNCATE TABLE #tmpCheckQueueMsgs;

		SET @errorTitle = 'lyrisListSync Queue Issue';
		SET @errorSubject = 'lyrisListSync queue moved items from processingItem to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- lyrisListSync / ReadyToProcess resend message
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -220, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_lyrisListSync WHERE statusID = @readyStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		update dbo.queue_lyrisListSync
		SET dateUpdated = getdate()
			OUTPUT inserted.itemID 
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE statusID = @readyStatusID 
		AND dateUpdated < @timeToUse;

		SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs;
		WHILE @itemAsStr IS NOT NULL BEGIN
			SET @xmlMessage = '<mc i="' + @itemAsStr + '" />';
			EXEC dbo.queue_lyrisListSync_sendMessage @xmlMessage=@xmlMessage;
			SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		END

		TRUNCATE TABLE #tmpCheckQueueMsgs;

		SET @errorTitle = 'lyrisListSync Queue Issue';
		SET @errorSubject = 'lyrisListSync queue resent items in ReadyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- lyrisListSync catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_lyrisListSync WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_lyrisListSync
		SET statusID = @readyStatusID, 
			dateUpdated = GETDATE()
			OUTPUT inserted.itemID 
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE dateUpdated < @timeToUse;

		-- resend messages
		SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs;
		WHILE @itemAsStr IS NOT NULL BEGIN
			SET @xmlMessage = '<mc i="' + @itemAsStr + '" />';
			EXEC dbo.queue_lyrisListSync_sendMessage @xmlMessage=@xmlMessage;
			SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		END

		TRUNCATE TABLE #tmpCheckQueueMsgs;

		SET @errorTitle = 'lyrisListSync Queue Issue';
		SET @errorSubject = 'lyrisListSync queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_lyrisListEmailChange_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);

	-- lyrisListEmailChange catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(autoID) FROM dbo.queue_lyrisListEmailChange WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'lyrisListEmailChange Queue Issue';
		SET @errorSubject = 'lyrisListEmailChange queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_lyrisExtIDChange_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);

	-- lyrisExtIDChange catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(autoID) FROM dbo.queue_lyrisExtIDChange WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'lyrisExtIDChange Queue Issue';
		SET @errorSubject = 'lyrisExtIDChange queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_listDigests_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse DATETIME, @errorSubject VARCHAR(400), @queueTypeID INT, @skippedStatusID INT, 
		@grabProcessingStatusID INT, @readyStatusID INT, @processingStatusID INT, @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='listDigests', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='Skipped', @queueStatusID=@skippedStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@grabProcessingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='Processing', @queueStatusID=@processingStatusID OUTPUT;

	-- listDigests / grabbedForProcessing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(MINUTE, -10, GETDATE());
	SELECT @issueCount = COUNT(itemID) FROM dbo.queue_listDigests WHERE statusID = @grabProcessingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_listDigests
		SET statusID = @readyStatusID, 
			dateUpdated = GETDATE()
		WHERE statusID = @grabProcessingStatusID 
		AND dateUpdated < @timeToUse;
		
		SET @errorTitle = 'listDigests Queue Issue';
		SET @errorSubject = 'listDigests queue moved items from grabbedForProcessing to ReadyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- listDigests / Processing autoreset to ReadyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(MINUTE, -10, GETDATE());
	SELECT @issueCount = COUNT(itemID) FROM dbo.queue_listDigests WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_listDigests
		SET statusID = @readyStatusID, 
			dateUpdated = GETDATE()
		WHERE statusID = @processingStatusID 
		AND dateUpdated < @timeToUse;
		
		SET @errorTitle = 'listDigests Queue Issue';
		SET @errorSubject = 'listDigests queue moved items from processing to ReadyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- notify if more than five attempts
	SELECT @issueCount = COUNT(itemID) FROM dbo.queue_listDigests WHERE previousAttempts >= 5;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'listDigests Queue Issue';
		SET @errorSubject = cast(@issueCount as VARCHAR(10)) + ' ListDigests queue items have errored and attempted at least five times.';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- catch all
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(HOUR, -4, GETDATE())
	SELECT @issueCount = COUNT(itemID) FROM dbo.queue_listDigests WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'listDigests Queue Issue';
		SET @errorSubject = 'listDigests queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- skipped items last updated more than two days
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(DAY, -2, GETDATE())
	SELECT @issueCount = COUNT(itemID) FROM dbo.queue_listDigests WHERE statusID = @skippedStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'listDigests Queue Issue';
		SET @errorSubject = 'listDigests queue has items with skipped status last updated more than 2 days ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END	

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_importSWODPrograms_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse DATETIME, @queueTypeID INT, @processingStatusID INT, @readyStatusID INT, 
		@readyNotifyStatusID INT, @grabNotifyStatusID INT, @itemAsStr VARCHAR(60), @xmlMessage xml, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='importSWODPrograms', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForNotifying', @queueStatusID=@grabNotifyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@readyNotifyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='Processing', @queueStatusID=@processingStatusID OUTPUT;

	-- importSWODPrograms / Processing autoreset to ReadyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemUID) FROM dbo.tblQueueItems WHERE queueStatusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.tblQueueItems
		SET queueStatusID = @readyStatusID, 
			dateUpdated = GETDATE()
		WHERE queueStatusID = @processingStatusID 
		AND dateUpdated < @timeToUse;

		SET @errorTitle = 'importSWODPrograms Queue Issue';
		SET @errorSubject = 'importSWODPrograms queue moved items from Processing to ReadyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- importSWODPrograms / readyToProcess resend message
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -220, GETDATE());
	SELECT @issueCount = count(itemUID) FROM dbo.tblQueueItems WHERE queueStatusID = @readyStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.tblQueueItems
		SET dateUpdated = GETDATE()
			OUTPUT inserted.itemUID 
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE queueStatusID = @readyStatusID 
		AND dateUpdated < @timeToUse;

		SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs;
		WHILE @itemAsStr IS NOT NULL BEGIN
			SET @xmlMessage = '<mc i="' + @itemAsStr + '" />';
			EXEC dbo.queue_importSWODPrograms_sendMessage @xmlMessage=@xmlMessage;
			SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		END

		TRUNCATE TABLE #tmpCheckQueueMsgs;
		
		SET @errorTitle = 'importSWODPrograms Queue Issue';
		SET @errorSubject = 'importSWODPrograms queue resent items in ReadyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- importSWODPrograms / readyToNotify or grabbedForNotifying with dateupdated older than 4 hours, something else in the group must be stopping it
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemUID) FROM dbo.tblQueueItems WHERE queueStatusID in (@readyNotifyStatusID,@grabNotifyStatusID) AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'importSWODPrograms Queue Issue';
		SET @errorSubject = 'importSWODPrograms queue has items in ReadyToNotify or GrabbedForNotifying last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- importSWODPrograms catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(qi.itemUID)
		FROM dbo.tblQueueItems AS qi 
		inner join dbo.tblQueueStatuses AS qs on qs.queueTypeID = @queueTypeID AND qs.queueStatusID = qi.queueStatusID 
		WHERE qi.dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'importSWODPrograms Queue Issue';
		SET @errorSubject = 'importSWODPrograms queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

