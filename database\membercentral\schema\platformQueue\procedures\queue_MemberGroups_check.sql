ALTER PROC dbo.queue_MemberGroups_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- dont run this if a full platform population is running; that process takes over an hour AND running this will muck it up
	IF EXISTS (SELECT top 1 runID FROM dataTransfer.dbo.cache_repopulation)
		GOTO on_done;

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @notReadyStatusID INT, @readyStatusID INT, 
		@orgID INT, @itemAsStr VARCHAR(60), @xmlMessage xml, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='memberGroups', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='notReady', @queueStatusID=@notReadyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;

	-- memberGroups / notReady resend message
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -15, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberGroups (READPAST) WHERE statusID = @notReadyStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		IF OBJECT_ID('tempdb..#tblNotReadyGroups') IS NOT NULL 
			DROP TABLE #tblNotReadyGroups;
		IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
			DROP TABLE #tblMCQRun;
		CREATE TABLE #tblNotReadyGroups (orgID INT, memberid INT);
		CREATE TABLE #tblMCQRun (orgID INT, memberID INT INDEX IX_tblMCQRun_memberID, conditionID INT);

		INSERT INTO #tblNotReadyGroups (orgID, memberID)
		SELECT orgID, memberID
		FROM dbo.queue_memberGroups (READPAST)
		WHERE statusID = @notReadyStatusID 
		AND dateUpdated < @timeToUse
		group by orgID, memberID;

		DELETE qi WITH (READPAST)
		FROM dbo.queue_memberGroups as qi
		INNER JOIN #tblNotReadyGroups as tmp 
			on tmp.orgID = qi.orgID 
			and qi.statusID = @notReadyStatusID
			AND tmp.memberID = qi.memberID;
		
		SELECT @orgID = min(orgID) FROM #tblNotReadyGroups;
		WHILE @orgID is not null BEGIN
			INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
			SELECT orgID, memberID, null
			FROM #tblNotReadyGroups
			WHERE orgID = @orgID;

			EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroups';

			TRUNCATE TABLE #tblMCQRun;

			SELECT @orgID = min(orgID) FROM #tblNotReadyGroups WHERE orgID > @orgID;
		END

		IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
			DROP TABLE #tblMCQRun;
		IF OBJECT_ID('tempdb..#tblNotReadyGroups') IS NOT NULL 
			DROP TABLE #tblNotReadyGroups;

		SET @errorTitle = 'memberGroups Queue Issue';
		SET @errorSubject = 'memberGroups queue resent items in notReady';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- memberGroups / readyToProcess resend message
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -15, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberGroups (READPAST) WHERE statusID = @readyStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_memberGroups WITH (UPDLOCK, READPAST)
		SET dateUpdated = getdate()
			OUTPUT inserted.itemGroupUID, inserted.orgID
			INTO #tmpCheckQueueMsgs (itemAsStr, orgID)
		WHERE statusID = @readyStatusID 
		AND dateUpdated < @timeToUse;

		SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs;
		while @itemAsStr is not null BEGIN
			SELECT @orgID = orgID from #tmpCheckQueueMsgs where itemAsStr = @itemAsStr;
			
			SELECT @xmlMessage = isnull((	
				SELECT @orgID as o, @itemAsStr as u	
				FOR XML RAW('mc'), TYPE	
			),'<mc/>');

			EXEC dbo.queue_memberGroups_sendMessage @xmlMessage=@xmlMessage;

			SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		end

		TRUNCATE TABLE #tmpCheckQueueMsgs;
		
		SET @errorTitle = 'memberGroups Queue Issue';
		SET @errorSubject = 'memberGroups queue resent items in readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- memberGroups catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -1, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberGroups (READPAST) WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'memberGroups Queue Issue';
		SET @errorSubject = 'memberGroups queue has items last updated more than 1 hour ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
