ALTER PROC dbo.queue_NJFirmSubStatements_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @grabProcessingStatusID INT, @readyStatusID INT, 
		@processingStatusID INT, @readyNotifyStatusID INT, @grabNotifyStatusID INT, 
		@errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='NJFirmSubStatements', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@grabProcessingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingFirm', @queueStatusID=@processingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@readyNotifyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@grabNotifyStatusID OUTPUT;

	-- NJFirmSubStatements / grabbedForProcessing autoreset to readyToProcess
	set @issueCount = 0;
	set @timeToUse = DATEADD(minute, -10, GETDATE());
	select @issueCount = count(itemID) from dbo.queue_NJFirmSubStatements where statusID = @grabProcessingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		update dbo.queue_NJFirmSubStatements
		set statusID = @readyStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @grabProcessingStatusID 
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='NJ Firm Billing Report Queue', @engine='BERLinux';

		SET @errorTitle = 'NJFirmSubStatements Queue Issue';
		SET @errorSubject = 'NJFirmSubStatements Queue moved items from grabbedForProcessing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- NJFirmSubStatements / processingFirm autoreset to readyToProcess
	set @issueCount = 0;
	set @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_NJFirmSubStatements WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		update dbo.queue_NJFirmSubStatements
		set statusID = @readyStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @processingStatusID 
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='NJ Firm Billing Report Queue', @engine='BERLinux';

		SET @errorTitle = 'NJFirmSubStatements Queue Issue';
		SET @errorSubject = 'NJFirmSubStatements queue moved items from processingFirm to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- NJFirmSubStatements / readyToNotify or grabbedForNotifying with dateupdated older than 4 hours, something else in the group must be stopping it
	set @issueCount = 0;
	set @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_NJFirmSubStatements WHERE statusID in (@readyNotifyStatusID,@grabNotifyStatusID) AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'NJFirmSubStatements Queue Issue';
		SET @errorSubject = 'NJFirmSubStatements queue has items in readyToNotify or grabbedForNotifying last updated more than 4 hours ago.';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- NJFirmSubStatements catchall
	set @issueCount = 0;
	set @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = COUNT(itemID) FROM dbo.queue_NJFirmSubStatements WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'NJFirmSubStatements Queue Issue';
		SET @errorSubject = 'NJFirmSubStatements Queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
