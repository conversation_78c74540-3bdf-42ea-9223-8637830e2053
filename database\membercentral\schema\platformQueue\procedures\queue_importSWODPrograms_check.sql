ALTER PROC dbo.queue_importSWODPrograms_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse DATETIME, @queueTypeID INT, @processingStatusID INT, @readyStatusID INT, 
		@readyNotifyStatusID INT, @grabNotifyStatusID INT, @itemAsStr VARCHAR(60), @xmlMessage xml, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='importSWODPrograms', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForNotifying', @queueStatusID=@grabNotifyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@readyNotifyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='Processing', @queueStatusID=@processingStatusID OUTPUT;

	-- importSWODPrograms / Processing autoreset to ReadyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemUID) FROM dbo.tblQueueItems WHERE queueStatusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.tblQueueItems
		SET queueStatusID = @readyStatusID, 
			dateUpdated = GETDATE()
		WHERE queueStatusID = @processingStatusID 
		AND dateUpdated < @timeToUse;

		SET @errorTitle = 'importSWODPrograms Queue Issue';
		SET @errorSubject = 'importSWODPrograms queue moved items from Processing to ReadyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- importSWODPrograms / readyToProcess resend message
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -220, GETDATE());
	SELECT @issueCount = count(itemUID) FROM dbo.tblQueueItems WHERE queueStatusID = @readyStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.tblQueueItems
		SET dateUpdated = GETDATE()
			OUTPUT inserted.itemUID 
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE queueStatusID = @readyStatusID 
		AND dateUpdated < @timeToUse;

		SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs;
		WHILE @itemAsStr IS NOT NULL BEGIN
			SET @xmlMessage = '<mc i="' + @itemAsStr + '" />';
			EXEC dbo.queue_importSWODPrograms_sendMessage @xmlMessage=@xmlMessage;
			SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		END

		TRUNCATE TABLE #tmpCheckQueueMsgs;
		
		SET @errorTitle = 'importSWODPrograms Queue Issue';
		SET @errorSubject = 'importSWODPrograms queue resent items in ReadyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- importSWODPrograms / readyToNotify or grabbedForNotifying with dateupdated older than 4 hours, something else in the group must be stopping it
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemUID) FROM dbo.tblQueueItems WHERE queueStatusID in (@readyNotifyStatusID,@grabNotifyStatusID) AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'importSWODPrograms Queue Issue';
		SET @errorSubject = 'importSWODPrograms queue has items in ReadyToNotify or GrabbedForNotifying last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- importSWODPrograms catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(qi.itemUID)
		FROM dbo.tblQueueItems AS qi 
		inner join dbo.tblQueueStatuses AS qs on qs.queueTypeID = @queueTypeID AND qs.queueStatusID = qi.queueStatusID 
		WHERE qi.dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'importSWODPrograms Queue Issue';
		SET @errorSubject = 'importSWODPrograms queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
