ALTER PROC dbo.queue_MemberUpdate_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID INT, @readyStatusID INT, @processingStatusID INT, @doneStatusID INT, @jobID INT,
		@errorSubject VARCHAR(400), @jobCount INT, @issueCount INT, @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='MemberImport', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='Processing', @queueStatusID=@processingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='done', @queueStatusID=@doneStatusID OUTPUT;

	IF OBJECT_ID('tempdb..#tmpMemImportJobs') IS NOT NULL 
		DROP TABLE #tmpMemImportJobs;
	CREATE TABLE #tmpMemImportJobs (jobID INT, readyToProcessMemCount INT, jobSinceMinutes INT);

	-- MemberImport / Done
	IF EXISTS (
		SELECT distinct imp.jobID
		FROM dbo.memimport_members as imp
		INNER JOIN dbo.memimport_jobs as j on j.jobID = imp.jobID
		WHERE imp.queueStatusID = @doneStatusID
		AND datediff(minute,j.dateEntered,getdate()) >= 30
			except
		SELECT distinct jobID
		FROM dbo.memimport_members
		WHERE queueStatusID <> @doneStatusID
	) BEGIN
		-- populate the log table for logging. we dont know the logID so just put 0.
		IF OBJECT_ID('tempdb..#tmpPMILogID') IS NOT NULL 
			DROP TABLE #tmpPMILogID;
		CREATE TABLE #tmpPMILogID (logID INT PRIMARY KEY);

		DECLARE @tmpJobs TABLE (jobID INT);
		
		INSERT INTO @tmpJobs (jobID)
		SELECT distinct imp.jobID
		FROM dbo.memimport_members as imp
		INNER JOIN dbo.memimport_jobs as j on j.jobID = imp.jobID
		WHERE imp.queueStatusID = @doneStatusID
		AND datediff(minute,j.dateEntered,getdate()) >= 30
			except
		SELECT distinct jobID
		FROM dbo.memimport_members
		WHERE queueStatusID <> @doneStatusID;

		SELECT @jobID = MIN(jobID) FROM @tmpJobs;
		WHILE @jobID IS NOT NULL BEGIN
			TRUNCATE TABLE #tmpPMILogID;

			INSERT INTO #tmpPMILogID (logID) VALUES (0);
			
			EXEC memberCentral.dbo.ams_importPartialMemberData_cancel @jobID=@jobID;

			SELECT @jobID = MIN(jobID) FROM @tmpJobs WHERE jobID > @jobID;
		END

		SET @errorTitle = 'MemberImport Queue Issue';
		SET @errorSubject = 'MemberImport job was stuck in done status for 30 minutes AND removed';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;

		IF OBJECT_ID('tempdb..#tmpPMILogID') IS NOT NULL 
			DROP TABLE #tmpPMILogID;
	END

	-- members stuck in readyToProcess for more than 30 minutes
	INSERT INTO #tmpMemImportJobs (jobID, readyToProcessMemCount, jobSinceMinutes)
	SELECT j.jobID, count(imp.memberID), datediff(minute,j.dateEntered,getDate())
	FROM dbo.memimport_jobs as j
	INNER JOIN dbo.memimport_members as imp on imp.jobID = j.jobID
	WHERE j.statusID = @readyStatusID
	AND imp.queueStatusID = @readyStatusID
	AND datediff(minute,j.dateEntered,getDate()) >= 30
	group by j.jobID, j.dateEntered;

	SET @jobCount = @@ROWCOUNT;

	IF @jobCount = 0
		GOTO on_done;

	SELECT @issueCount = COUNT(*)
	FROM #tmpMemImportJobs AS tmp
	WHERE tmp.readyToProcessMemCount = 1
	AND NOT EXISTS (SELECT 1 FROM dbo.memimport_members as imp WHERE imp.jobID = tmp.jobID AND imp.queueStatusID <> @readyStatusID);

	-- if a member is stuck in readyToProcess for more than 30 minutes and there are no other members in readyToProcess for any jobs
	IF @jobCount = 1 AND @issueCount = 1 BEGIN
		SELECT @jobID = jobID FROM #tmpMemImportJobs;
		EXEC dbo.queue_MemberUpdate_load @jobID=@jobID;

		SET @errorTitle = 'MemberImport Queue Issue';
		SET @errorSubject = 'MemberImport job member stuck in readyToProcess for more than 30 minutes AND retried';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END
	
	-- multiple members stuck in readyToProcess for more than 4 hours
	ELSE IF EXISTS (SELECT 1 FROM #tmpMemImportJobs WHERE readyToProcessMemCount > 0 AND jobSinceMinutes >= 240) BEGIN
		SET @jobID = NULL;
		SELECT @jobID = MIN(jobID) FROM #tmpMemImportJobs WHERE readyToProcessMemCount > 0 AND jobSinceMinutes >= 240;

		WHILE @jobID IS NOT NULL BEGIN
			EXEC dbo.queue_MemberUpdate_load @jobID=@jobID;
			SELECT @jobID = MIN(jobID) FROM #tmpMemImportJobs WHERE jobID > @jobID AND readyToProcessMemCount > 0 AND jobSinceMinutes >= 240;
		END

		SET @errorTitle = 'MemberImport Queue Issue';
		SET @errorSubject = 'MemberImport job members stuck in readyToProcess for more than 4 hours AND retried';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END
	
	on_done:
	IF OBJECT_ID('tempdb..#tmpMemImportJobs') IS NOT NULL 
		DROP TABLE #tmpMemImportJobs;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
