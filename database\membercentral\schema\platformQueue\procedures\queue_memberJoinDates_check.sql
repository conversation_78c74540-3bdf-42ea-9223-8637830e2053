ALTER PROC dbo.queue_memberJoinDates_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse DATETIME, @queueTypeID INT, @processingStatusID INT, @readyStatusID INT, 
		@itemAsStr VARCHAR(60), @xmlMessage xml, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='memberJoinDates', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processing', @queueStatusID=@processingStatusID OUTPUT;

	-- memberJoinDates / processing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberJoinDates WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_memberJoinDates
		SET statusID = @readyStatusID, 
			dateUpdated = GETDATE()
			OUTPUT inserted.itemID 
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE statusID = @processingStatusID 
		AND dateUpdated < @timeToUse;

		SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs;
		WHILE @itemAsStr IS NOT NULL BEGIN
			SET @xmlMessage = '<mc i="' + @itemAsStr + '" />';
			EXEC dbo.queue_memberJoinDates_sendMessage @xmlMessage=@xmlMessage;
			SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		END

		TRUNCATE TABLE #tmpCheckQueueMsgs;

		SET @errorTitle = 'memberJoinDates Queue Issue';
		SET @errorSubject = 'memberJoinDates queue moved items from processing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- memberJoinDates / ReadyToProcess resend message
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -220, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberJoinDates WHERE statusID = @readyStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		update dbo.queue_memberJoinDates
		SET dateUpdated = getdate()
			OUTPUT inserted.itemID 
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE statusID = @readyStatusID 
		AND dateUpdated < @timeToUse;

		SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs;
		WHILE @itemAsStr IS NOT NULL BEGIN
			SET @xmlMessage = '<mc i="' + @itemAsStr + '" />';
			EXEC dbo.queue_memberJoinDates_sendMessage @xmlMessage=@xmlMessage;
			SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		END

		TRUNCATE TABLE #tmpCheckQueueMsgs;

		SET @errorTitle = 'memberJoinDates Queue Issue';
		SET @errorSubject = 'memberJoinDates queue resent items in ReadyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- memberJoinDates catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_memberJoinDates WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'memberJoinDates Queue Issue';
		SET @errorSubject = 'memberJoinDates queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
