ALTER PROC dbo.queue_s3Download_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @readyStatusID INT, @failedStatusID INT, 
		@errorSubject VARCHAR(400), @errorTitle VARCHAR(400), @tier varchar(12);
	EXEC dbo.queue_getQueueTypeID @queueType='s3Download', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='failed', @queueStatusID=@failedStatusID OUTPUT;

	-- We dont download when not in production, so delete the queue when not in prod
	SELECT @tier = tier from membercentral.dbo.fn_getServerSettings();
	IF @tier = 'Production' BEGIN
		-- s3Download readyToProcess notify
		SET @issueCount = 0;
		SET @timeToUse = DATEADD(hour, -4, GETDATE());
		SELECT @issueCount = count(fileID) FROM dbo.queue_S3Download WHERE statusID = @readyStatusID AND dateUpdated < @timeToUse;
		IF @issueCount > 0 BEGIN
			SET @errorTitle = 'S3Download Queue Issue';
			SET @errorSubject = 'S3Download queue has items in readyToProcess with dateUpdated older than 4 hours.';
			EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
		END

		-- s3Download failed notify. Use DateAdded since we change dateupdated upon fail but still keep trying to process it.
		SET @issueCount = 0;
		SET @timeToUse = DATEADD(hour, -4, GETDATE());
		SELECT @issueCount = count(fileID) FROM dbo.queue_S3Download WHERE statusID = @failedStatusID AND dateAdded < @timeToUse;
		IF @issueCount > 0 BEGIN
			SET @errorTitle = 'S3Download Queue Issue';
			SET @errorSubject = 'S3Download queue has items in failed status with dateAdded older than 4 hours.';
			EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
		END
	END ELSE BEGIN
		SELECT @issueCount = count(fileID) FROM dbo.queue_S3Download;
		IF @issueCount > 0
			DELETE FROM dbo.queue_S3Download;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
