ALTER PROC dbo.queue_payTSBalance_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @grabProcessingStatusID INT, @readyStatusID INT, 
		@processingStatusID INT, @itemAsStr VARCHAR(60), @xmlMessage xml, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='payTSBalance', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@grabProcessingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingPayment', @queueStatusID=@processingStatusID OUTPUT;

	-- payTSBalance / grabbedForProcessing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -30, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_payTSBalance WHERE statusID = @grabProcessingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_payTSBalance
		SET statusID = @readyStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @grabProcessingStatusID 
		AND dateUpdated < @timeToUse;

		SET @errorTitle = 'PayTSBalance Queue Issue';
		SET @errorSubject = 'PayTSBalance Queue moved items from grabbedForProcessing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- payTSBalance / processingPayment with dateupdated older than 10 mins, something went wrong
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_payTSBalance WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'PayTSBalance Queue Issue';
		SET @errorSubject = 'PayTSBalance queue has items in processingPayment with dateupdated older than 10 minutes.';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- payTSBalance catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = COUNT(itemID) FROM dbo.queue_payTSBalance WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'PayTSBalance Queue Issue';
		SET @errorSubject = 'PayTSBalance Queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
