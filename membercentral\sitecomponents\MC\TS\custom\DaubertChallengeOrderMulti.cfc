<cfcomponent extends="model.customPage.customPage" output="true">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// This custom form is no longer needed as the functionality is now built into the search application
			// Redirect to the Daubert Challenge Study search bucket
			local.bucketID = getBucketIDForDaubertStudy(arguments.event.getValue('mc_siteInfo.siteID'));

			if (local.bucketID > 0) {
				// Redirect to the search bucket
				local.redirectURL = "/?pg=search&bid=#local.bucketID#";
			} else {
				// Fallback to general search page if bucket not found
				local.redirectURL = "/?pg=search";
			}

			// Perform the redirect
			location(url=local.redirectURL, addtoken=false);
		</cfscript>
	</cffunction>

	<cffunction name="getBucketIDForDaubertStudy" access="private" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.bucketID = 0>

		<cftry>
			<cfquery name="local.qryBucket" datasource="#application.dsn.tlasites_search.dsn#">
				SELECT sb.bucketID
				FROM dbo.tblSearchBuckets sb
				INNER JOIN dbo.tblSearchBucketTypes sbt ON sb.bucketTypeID = sbt.bucketTypeID
					AND sbt.isActive = 1
					AND sbt.bucketType = 'DaubertStudy'
				WHERE sb.siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">
				AND sb.isActive = 1
			</cfquery>

			<cfif local.qryBucket.recordCount GT 0>
				<cfset local.bucketID = local.qryBucket.bucketID>
			</cfif>

		<cfcatch type="any">
			<cfset local.bucketID = 0>
		</cfcatch>
		</cftry>

		<cfreturn local.bucketID>
	</cffunction>

</cfcomponent>