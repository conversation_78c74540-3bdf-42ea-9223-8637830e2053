<cfif NOT local.strResponse.isValidResponse>
	<cfoutput>
	<div class="m-2">
		<div class="alert alert-danger mb-2">Invalid response</div>
	</div>
	</cfoutput>
<cfelse>
	<cfoutput>
	<div class="m-2">
		<div class="form-group row">
			<label class="col-3 col-form-label pt-0 pb-0"><b>Respondent:</b></label>
			<div class="col-9 col-form-label pt-0 pb-0">
				#local.strResponse.qryResponse.lastname#, #local.strResponse.qryResponse.firstname#<cfif local.strResponse.qryResponse.signuporgcode eq local.siteCode> (#local.strResponse.qryResponse.memberNumber#)</cfif>
				<cfif len(local.strResponse.qryResponse.company)><br/>#local.strResponse.qryResponse.company#</cfif>
			</div>
		</div>
		<div class="form-group row">
			<label class="col-3 col-form-label pt-0 pb-0"><b>Response Started:</b></label>
			<div class="col-9 col-form-label pt-0 pb-0">#dateTimeFormat(local.strResponse.qryResponse.dateDelivered,'m/d/yyyy h:nn tt')# CT</div>
		</div>
		<div class="form-group row">
			<label class="col-3 col-form-label pt-0 pb-0"><b>Response Completed:</b></label>
			<div class="col-9 col-form-label pt-0 pb-0">#dateTimeFormat(local.strResponse.qryResponse.dateCompleted,'m/d/yyyy h:nn tt')# CT</div>
		</div>
		<cfif local.strResponse.qryForm.formTypeAbbr EQ 'E'>	
			<div class="form-group row">
				<label class="col-3 col-form-label pt-0 pb-0"><b>Exam Score:</b></label>
				<div class="col-9 col-form-label pt-0 pb-0">#local.strResponse.qryResponse.passingPct#%</div>
			</div>
		</cfif>
		<h5 class="mt-4 mb-2">#local.strResponse.qryForm.formTitle#</h5>
		<div class="ml-2 mb-2">#local.strResponse.qryForm.formintro#</div>
		<div class="mb-4">
			<cfloop query="local.strResponse.qrySections">
				<cfset local.questionNumber = 0>
				<cfset var theSectionID = local.strResponse.qrySections.sectionID>
				<cfset local.qryQuestions = local.strResponse.qryQuestions.filter(function(row) { return arguments.row.sectionID EQ theSectionID; })>

				<cfif len(local.strResponse.qrySections.sectionTitle)>
					<h5 class="mb-2">#local.strResponse.qrySections.sectionTitle#</h5>
				</cfif>
				<cfif len(local.strResponse.qrySections.sectionDesc)>
					<div class="mb-2">#local.strResponse.qrySections.sectionDesc#</div>
				</cfif>

				<cfset var theQuestionID = 0>
				<cfloop query="local.qryQuestions">
					<cfset theQuestionID = local.qryQuestions.questionID>
					<cfset local.qryResponse = local.strResponse.qryResponseDetail.filter(function(row) { return arguments.row.questionID EQ theQuestionID; })>
					
					<table width="100%" class="mb-2">
					<tr>
						<cfif local.strResponse.qryForm.formTypeAbbr EQ 'E'>
							<td width="10" valign="top">
								<cfif local.qryResponse.isCorrect EQ 1>
									<i class="fa-solid fa-circle-check text-success"></i>
								<cfelse>
									<i class="fa-solid fa-circle-xmark text-danger"></i>
								</cfif>
							</td>
						</cfif>
						<td valign="top" style="text-align:right;" width="20">
							<cfif local.qryQuestions.displayquestionnumber>
								<cfset local.questionNumber = local.questionNumber + 1>
								<b>#local.questionNumber#.</b>
							<cfelse>
								&nbsp;    
							</cfif> 
						</td>
						<td valign="top">
							<b>#local.qryQuestions.questiontext#</b>
						</td>
					</tr>
					<tr>
						<cfif local.strResponse.qryForm.formTypeAbbr EQ 'E'>
							<td>&nbsp;</td>
						</cfif>
						<td>&nbsp;</td>
						<td>
							<cfif listFind("1,2,10",local.qryQuestions.questiontypeid)>
								<div class="alert alert-info p-1">#EncodeForHTML(local.qryResponse.responseText)#</div>
							<cfelseif listFind("3,4,5,6",local.qryQuestions.questiontypeid)>
								<div class="alert <cfif local.strResponse.qryForm.formTypeAbbr EQ 'E'><cfif local.qryResponse.isCorrect EQ 1>alert-success<cfelse>alert-danger</cfif><cfelse>alert-info</cfif> p-1">
									<cfloop query="local.qryResponse">
										<div>#EncodeForHTML(local.qryResponse.optionText)#</div>
									</cfloop>
								</div>
							<cfelseif listFind("7,8,11",local.qryQuestions.questiontypeid)>
								<cfloop query="local.qryResponse">
									<div class="form-group row ml-0">
										<label class="col-7 col-form-label p-0">#EncodeForHTML(local.qryResponse.optionText)#</label>
										<div class="col-4 col-form-label alert alert-info p-1 mb-0">#EncodeForHTML(local.qryResponse.optionXText)#</div>
									</div>									
								</cfloop>
							</cfif>
						</td>
					</tr>
					</table>
				</cfloop>
			</cfloop>
		</div>  
	</div>
	</cfoutput>
</cfif>