ALTER PROC dbo.queue_refreshMemberPhoto_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @processingStatusID INT, @readyStatusID INT, 
		@itemAsStr VARCHAR(60), @xmlMessage xml, @errorSubject VARCHAR(400), @errorTitle varchar(400);
	EXEC dbo.queue_getQueueTypeID @queueType='refreshMemberPhoto', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='Processing', @queueStatusID=@processingStatusID OUTPUT;

	-- refreshMemberPhoto / Processing autoreset to ReadyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -20, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_refreshMemberPhoto WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_refreshMemberPhoto
		SET statusID = @readyStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @processingStatusID 
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='Process Refresh MemberPhoto Queue', @engine='BERLinux';

		SET @errorTitle = 'refreshMemberPhoto Queue Issue';
		SET @errorSubject = 'refreshMemberPhoto queue moved items from Processing to ReadyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- refreshMemberPhoto catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_refreshMemberPhoto WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'refreshMemberPhoto Queue Issue';
		SET @errorSubject = 'refreshMemberPhoto queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
