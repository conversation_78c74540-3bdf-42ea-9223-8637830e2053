ALTER PROC dbo.queue_payInvoices_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @grabProcessingStatusID INT, @readyStatusID INT, 
		@processingPayStatusID INT, @processingAllocStatusID INT, @readyNotifyStatusID INT, @grabNotifyStatusID INT, 
		@errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='payInvoices', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@grabProcessingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingPayment', @queueStatusID=@processingPayStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingAllocation', @queueStatusID=@processingAllocStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@readyNotifyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@grabNotifyStatusID OUTPUT;

	-- PayInvoices / grabbedForProcessing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -30, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_payInvoices WHERE statusID = @grabProcessingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_payInvoices
		SET statusID = @readyStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @grabProcessingStatusID 
		AND dateUpdated < @timeToUse;

		SET @errorTitle = 'PayInvoices Queue Issue';
		SET @errorSubject = 'PayInvoices Queue moved items from grabbedForProcessing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- PayInvoices / processingPayment or processingAllocation with dateupdated older than 10 mins, something else in the group must be stopping it
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_payInvoices WHERE statusID in (@processingPayStatusID,@processingAllocStatusID) AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'PayInvoices Queue Issue';
		SET @errorSubject = 'PayInvoices queue has items in processingPayment or processingAllocation with dateupdated older than 10 minutes.';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- PayInvoices / readyToNotify or grabbedForNotifying with dateupdated older than 2 hours, something else in the group must be stopping it
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -2, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_payInvoices WHERE statusID in (@readyNotifyStatusID,@grabNotifyStatusID) AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'PayInvoices Queue Issue';
		SET @errorSubject = 'PayInvoices queue has items in readyToNotify or grabbedForNotifying with last updated more than 2 hours ago.';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- PayInvoices catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = COUNT(itemID) FROM dbo.queue_payInvoices WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'PayInvoices Queue Issue';
		SET @errorSubject = 'PayInvoices Queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
