<cfoutput>
	<cfset local.pageTitle=trim(event.getValue( 'mc_pageDefinition.originalPageTitle',event.getValue( 'mc_pageDefinition.pageName')))/>
	<cfset local.isSideEnabled = false />
	<cfset local.headerBackground = ""/>
	<cfif application.objCMS.getZoneItemCount(zone='L',event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['L'],1)>
			<cfset local.headerBackground = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['L'][1].data,"<p>",""),"</p>",""))/> 
		</cfif>
	</cfif>
	<cfif len(local.headerBackground) eq 0 and application.objCMS.getZoneItemCount(zone='K',event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['K'],1)>
			<cfset local.headerBackground = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['K'][1].data,"<p>",""),"</p>",""))/>
		</cfif>
	</cfif>
	<cfif len(local.headerBackground) eq 0>
		<cfset local.headerBackground = '<img src="/images/inner-defaultbanner.jpg"/>'>
	</cfif>
	<cfset local.zoneT1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='T' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['T'],1)>
			<cfset local.zoneT1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['T'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	<cfset local.zoneN1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='N' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['N'],1)>
			<cfset local.zoneN1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['N'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	<cfset local.zoneO1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='O' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['O'],1)>
			<cfset local.zoneO1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['O'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	<cfset local.zoneP1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='P' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['P'],1)>
			<cfset local.zoneP1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['P'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>

	<cfif len(local.zoneN1Content) OR len(local.zoneO1Content) OR len(local.zoneP1Content)>
		<cfset local.isSideEnabled=true /> 
	</cfif>
	<!doctype html>
	<html lang="en">
		<head>
			<cfinclude template="head.cfm">		
		</head>
		<cfif event.getValue('mc_pageDefinition.layoutMode','normal') neq "direct">
			<body>
				<div class="wrapper">
					<cfinclude template="header.cfm">
					<!-- Inner Banner Start -->
					<div class="bannerInner bannerdesign2">
						#local.headerBackground#
						<div class="container containerCustom">
							<div class="row d-flex-wrap">
								<div class="banner-info">
									<cfif len(trim(local.zoneT1Content))>
									<p class="subHeadline">#local.zoneT1Content#</p>									
									</cfif>
									<h1 class="TitleText <cfif len(local.pageTitle) gt 25> lgTitle </cfif>">#local.pageTitle#  &nbsp;&nbsp;</h1>
									<div aria-label="breadcrumb" class="breadcrumbs-wrap <cfif len(local.pageTitle) gt 25> lgTitleBreadCrumb </cfif>">
										<ol class="breadcrumb">
											<li class="breadcrumb-item">
												<a href="/"><img src="/images/icon-home.png" alt="" /> Home</a>
											</li>
											<cfif len(event.getValue('mc_pageDefinition.sectionBreadcrumb'))>
												<li class="breadcrumb-item"><a href="/?pg=#event.getValue('mc_pageDefinition.sectionBreadcrumb')#">#event.getValue('mc_pageDefinition.sectionName')#</a></li>
											</cfif>
											<li class="breadcrumb-item active" aria-current="page">#local.pageTitle#</li>
										</ol>
									</div>
								</div>
							</div>
						</div>
					</div>
					<!-- Inner Banner End -->

					<!-- Main Content Start -->
					<div class="content">
						<div class="inner-page-content ">
							<div class="row-fluid">
								<div class="innerPage-content <cfif len(local.pageTitle) gt 25> innerCntMargin </cfif> <cfif local.isSideEnabled and event.getValue('mc_pageDefinition.layoutMode','normal') neq "full">span8<cfelse>span12</cfif> inner-content-area"  >
									<cfif application.objCMS.getZoneItemCount(zone='Main' ,event=event)> #application.objCMS.renderZone(zone='Main',event=event)# </cfif>
								</div>
								<cfif local.isSideEnabled and event.getValue( 'mc_pageDefinition.layoutMode', 'normal') neq "full">
								<div class="span4 sidebar">
									<div class="quicklink-desktop">
										<div class="eventbox-row">
											<span class="zoneN1Holder"></span>
											<span class="zoneO1Holder"></span>
											<cfif len(trim(local.zoneP1Content))>
												<div class="eventbox-col">
													<div class="eventbox-info zoneP1Holder show">
														#local.zoneP1Content#
													</div>
												</div>
											</cfif>
										</div>
									</div>
								</div>
								</cfif>
							</div>
						</div>
					</div>
					<!-- Main Content End -->
					<span class="zoneN1Wrapper hide">
						#local.zoneN1Content#
					</span>
					<span class="zoneO1Wrapper hide">
						#local.zoneO1Content#
					</span>

					<!--Content End-->
					<cfinclude template="footer.cfm">
					<cfinclude template="toolBar.cfm">
				</div>
			</body>
		<cfelse>
			<body style="background:none!important;background:unset!important;padding:20px;" class="innerPage-content">
				#application.objCMS.renderZone(zone='Main',event=event)#
			</body>		
		</cfif>
		<cfinclude template="foot.cfm">
	</html>
</cfoutput>